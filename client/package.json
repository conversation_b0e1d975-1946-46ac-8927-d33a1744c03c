{"repository": "sharetribe/sharetribe", "license": "MIT", "engines": {"node": "10.15.3", "npm": "6.4.1"}, "scripts": {"ensure-node-version": "check-node-version --package", "lint": "npm run eslint && npm run stylelint", "eslint": "npm run ensure-node-version && eslint .", "stylelint": "npm run ensure-node-version && stylelint **/*.css", "build:client": "npm run ensure-node-version && webpack --mode=production --config webpack.client.config.js", "build:server": "npm run ensure-node-version && webpack --mode=production --config webpack.server.config.js", "build:production:client": "npm run ensure-node-version && NODE_ENV=production webpack --mode=production --config webpack.client.config.js", "build:production:server": "npm run ensure-node-version && NODE_ENV=production webpack --mode=production --config webpack.server.config.js", "build:production": "npm run ensure-node-version && npm run build:production:client && npm run build:production:server", "build:dev:client": "npm run ensure-node-version && webpack --mode=development -w --config webpack.client.config.js", "build:dev:server": "npm run ensure-node-version && webpack --mode=development -w --config webpack.server.config.js", "build:test": "npm run ensure-node-version && npm run build:client && npm run build:server", "print-phantomjs-version": "npm run ensure-node-version && phantomjs --version", "start-phantomjs": "npm run ensure-node-version && phantomjs --webdriver=8910", "styleguide": "npm run ensure-node-version && start-storybook -p 9001", "deploy-storybook": "npm run ensure-node-version && storybook-to-ghpages", "test:stories": "npm run ensure-node-version && mocha './app/components/**/*.story.js' --reporter mocha-circleci-reporter --require @babel/register --require ignore-styles --require ./mochaConfig.js", "test:specs": "npm run ensure-node-version && mocha './app/specs/*.spec.js' --reporter mocha-circleci-reporter --require @babel/register --require ignore-styles --require ./mochaConfig.js", "test": "npm run ensure-node-version && npm run test:stories && npm run test:specs", "test:devspecs": "mocha './app/specs/*.spec.js' --require @babel/register", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook"}, "dependencies": {"@babel/cli": "7.4.4", "@babel/core": "7.4.4", "@babel/plugin-proposal-decorators": "7.4.4", "@babel/plugin-proposal-export-namespace-from": "7.2.0", "@babel/plugin-proposal-function-sent": "7.2.0", "@babel/plugin-proposal-numeric-separator": "7.2.0", "@babel/plugin-proposal-throw-expressions": "7.2.0", "@babel/polyfill": "7.4.4", "@babel/preset-env": "7.4.4", "@babel/preset-react": "7.0.0", "@babel/runtime": "7.4.4", "axios": "^0.21.2", "babel-loader": "8.2.5", "check-node-version": "3.3.0", "classnames": "2.2.5", "css-loader": "3.0.0", "es6-shim": "0.35.6", "file-loader": "3.0.1", "i18n-js": "http://github.com/sharetribe/i18n-js/archive/interpolation-mode.tar.gz", "immutable": "3.8.1", "imports-loader": "0.6.5", "loader-utils": "0.2.16", "lodash": "^4.17.21", "lodash.merge": "^4.6.2", "mini-css-extract-plugin": "0.6.0", "moment": "2.29.2", "node-libs-browser": "2.0.0", "node-sass": "4.13.1", "numbro": "1.9.3", "postcss-cssnext": "3.1.0", "postcss-loader": "3.0.0", "postcss-mixins": "6.2.1", "r-dom": "2.3.1", "raw-loader": "2.0.0", "react": "15.4.1", "react-addons-css-transition-group": "15.4.1", "react-addons-shallow-compare": "15.4.1", "react-addons-transition-group": "15.4.1", "react-dates": "4.1.0", "react-dom": "15.4.1", "react-form": "git+https://github.com/ithouse/react-form.git#8053d0c9093c35843522b734fb5a1777b35f1c48", "react-on-rails": "13.0.2", "react-redux": "4.4.6", "redux": "3.6.0", "redux-thunk": "2.1.0", "resolve-url-loader": "1.6.0", "sass-loader": "7.1.0", "style-loader": "0.23.1", "transit-js": "0.8.846", "uglifyjs-webpack-plugin": "2.2.0", "url-loader": "1.1.2", "webpack": "4.30.0", "webpack-cli": "3.3.0", "whatwg-fetch": "3.6.2"}, "devDependencies": {"babel-eslint": "10.0.1", "babel-plugin-react-transform": "3.0.0", "@babel/register": "7.4.4", "chai": "3.5.0", "chai-enzyme": "0.6.1", "enzyme": "2.6.0", "eslint": "4.18.2", "eslint-plugin-babel": "4.1.2", "eslint-plugin-react": "6.4.1", "ignore-styles": "5.0.1", "jsdom": "9.8.3", "json": "10.0.0", "mocha": "6.1.4", "mocha-circleci-reporter": "0.0.3", "phantomjs-prebuilt": "^2.1.16", "react-addons-test-utils": "15.4.1", "react-transform-hmr": "1.0.4", "@storybook/react": "^4.1.18", "@storybook/addon-actions": "^4.1.18", "@storybook/addon-links": "^4.1.18", "@storybook/addons": "^4.1.18", "storybook-addon-specifications": "2.1.2", "stylelint": "10.0.1", "stylelint-config-standard": "18.3.0", "webpack-dev-middleware": "3.6.1", "webpack-dev-server": "3.3.1"}, "browser": {"fs": false}}