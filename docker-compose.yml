services:
  db:
    image: postgres:14
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: sharetribe_development
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  sphinx:
    image: macbre/sphinxsearch:3.7.1
    restart: always
    ports:
      - "9306:9306"
      - "9312:9312"
    volumes:
      - ./sphinx.conf:/opt/sphinx/conf/sphinx.conf
      - sphinx_data:/var/lib/sphinxsearch/data
    depends_on:
      db:
        condition: service_healthy

  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  memcached:
    image: memcached:1.6-alpine
    restart: always
    ports:
      - "11211:11211"
    command: memcached -m 64

  web:
    build: .
    ports:
      - "3000:3000"
    env_file: .env.development
    environment:
      - RAILS_ENV=development
      - DATABASE_URL=******************************************************
      - SPHINX_HOST=sphinx
      - WEBPACKER_DEV_SERVER_HOST=0.0.0.0
      - RAILS_MASTER_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
    depends_on:
      db:
        condition: service_healthy
      sphinx:
        condition: service_started
      memcached:
        condition: service_started
      redis:
        condition: service_healthy
    volumes:
      - .:/opt/app
      - bundle_cache:/usr/local/bundle
    entrypoint: /opt/app/docker-entrypoint.sh
    command: bash -c "cd /opt/app && bundle exec rails server -b 0.0.0.0"

  worker:
    build: .
    env_file: .env.development
    environment:
      - RAILS_ENV=development
      - DATABASE_URL=******************************************************
      - SPHINX_HOST=sphinx
      - QUEUES=default,paperclip,mailers
      - MAGICK_MAP_LIMIT=64MiB
      - MAGICK_MEMORY_LIMIT=256MiB
      - MAGICK_TIME_LIMIT=30
      - RAILS_MASTER_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
    depends_on:
      db:
        condition: service_healthy
      sphinx:
        condition: service_started
      memcached:
        condition: service_started
      redis:
        condition: service_healthy
      web:
        condition: service_started
    volumes:
      - .:/opt/app
      - bundle_cache:/usr/local/bundle
    entrypoint: /opt/app/docker-entrypoint.sh
    command: bash -c "cd /opt/app && bundle exec rake jobs:work"

volumes:
  postgres_data:
  sphinx_data:
  bundle_cache:
  redis_data:
